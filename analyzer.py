"""
Core analysis functionality for the Layout Bricks Instructions Analyzer.

This module contains the main LayoutBricksAnalyzer class that orchestrates
the analysis of text files using LiteLLM and OpenRouter.
"""

import json
import asyncio
import logging
import litellm
from pathlib import Path
from typing import Dict, List, Set

from models import AnalysisResult
from rate_limiter import RateLimiter
from file_utils import find_txt_files, load_processed_files, save_reasoning_file
from csv_handler import save_to_csv
from prompt_manager import Prompt<PERSON>anager
from config import Config
from error_handler import ErrorHandler

logger = logging.getLogger(__name__)


class LayoutBricksAnalyzer:
    """Main analyzer class for processing text files."""

    def __init__(self, api_key: str = None, prompt_file: str = "_prompt.txt",
                 max_workers: int = 4, max_calls_per_minute: int = 18):
        """Initialize the analyzer with OpenRouter API key and prompt file."""
        self.config = Config()
        self.api_key = api_key or self.config.api_key

        # Initialize components
        self.prompt_manager = PromptManager(prompt_file)
        self.rate_limiter = RateLimiter(max_calls_per_minute)
        self.processed_files: Set[str] = set()
        self.max_workers = max_workers
        self.error_handler = ErrorHandler(self.config)



    async def analyze_file(self, file_path: Path, output_folder: Path = None) -> Dict:
        """Analyze a single file for layout bricks instructions."""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": "File is empty",
                    "keywords_found": [],
                    "confidence": "high",
                    "relevant_segments": "",
                    "error": None
                }

            # Create prompt
            prompt = self.prompt_manager.create_analysis_prompt(content)

            # Wait for rate limiter permission
            await self.rate_limiter.acquire()

            # Call LiteLLM with OpenRouter using structured output
            try:
                response = await asyncio.to_thread(
                    litellm.completion,
                    model=self.config.model,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant designed to output JSON for analyzing text content."},
                        {"role": "user", "content": prompt}
                    ],
                    response_format=AnalysisResult,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens,
                    api_key=self.api_key
                )
            except Exception as api_error:
                safe_file_path = str(file_path).encode('ascii', errors='replace').decode('ascii')
                logger.error(f"API call failed for {safe_file_path}: {api_error}")
                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"API call failed: {str(api_error)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "relevant_segments": "",
                    "error": str(api_error)
                }

            # Parse structured response
            try:
                response_text = response.choices[0].message.content
                if not response_text:
                    raise ValueError("Empty response content from API")
                logger.debug(f"Structured response for {file_path}: {response_text}")
            except (IndexError, AttributeError, ValueError) as response_error:
                safe_file_path = str(file_path).encode('ascii', errors='replace').decode('ascii')
                error_msg = f"Invalid API response structure: {str(response_error)}"
                logger.error(f"{error_msg} for {safe_file_path}")

                # Try to dump whatever response we got
                try:
                    response_dump = str(response) if 'response' in locals() else "No response object"
                    dump_path = self.error_handler.dump_response_to_file(response_dump, file_path, error_msg, output_folder)
                    logger.info(f"Response object dumped: {dump_path}")
                except Exception as dump_error:
                    logger.warning(f"Could not dump response object: {dump_error}")
                    dump_path = "Failed to dump response"

                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"Invalid API response: {str(response_error)}. Response dump: {dump_path}",
                    "keywords_found": [],
                    "confidence": "low",
                    "relevant_segments": "",
                    "error": str(response_error)
                }

            # Parse the JSON response (should be valid JSON now)
            try:
                # Clean the response text to handle leading/trailing whitespace and newlines
                cleaned_response = response_text.strip()

                # Additional cleaning for common JSON issues
                if cleaned_response.startswith('```json'):
                    # Remove markdown code block markers
                    cleaned_response = cleaned_response[7:]  # Remove ```json
                    if cleaned_response.endswith('```'):
                        cleaned_response = cleaned_response[:-3]  # Remove ```
                    cleaned_response = cleaned_response.strip()

                # Try to fix common JSON issues
                if not cleaned_response.startswith('{'):
                    # Find the first { character
                    start_idx = cleaned_response.find('{')
                    if start_idx != -1:
                        cleaned_response = cleaned_response[start_idx:]

                if not cleaned_response.endswith('}'):
                    # Find the last } character
                    end_idx = cleaned_response.rfind('}')
                    if end_idx != -1:
                        cleaned_response = cleaned_response[:end_idx + 1]

                analysis_result = json.loads(cleaned_response)

            except json.JSONDecodeError as e:
                # Use error handler to handle JSON parsing error
                analysis_result = self.error_handler.handle_json_parse_error(
                    response_text, file_path, e, output_folder
                )

            # Add file path and ensure score is valid
            analysis_result["file_path"] = str(file_path)
            analysis_result["score"] = max(1, min(10, int(analysis_result.get("score", 1))))
            analysis_result["error"] = None

            # Ensure relevant_segments field exists with default value
            if "relevant_segments" not in analysis_result:
                analysis_result["relevant_segments"] = ""

            return analysis_result

        except Exception as e:
            # Create a safe file path for logging (remove problematic Unicode characters)
            safe_file_path = str(file_path).encode('ascii', errors='replace').decode('ascii')
            logger.error(f"Error analyzing file {safe_file_path}: {e}")

            # If we have a response but failed for other reasons, try to dump it
            dump_path = "No response to dump"
            try:
                if 'response' in locals() and hasattr(response, 'choices') and response.choices:
                    response_content = response.choices[0].message.content
                    if response_content:
                        error_msg = f"General analysis error: {str(e)}"
                        dump_path = self.error_handler.dump_response_to_file(response_content, file_path, error_msg, output_folder)
                        logger.info(f"Response dumped due to general error: {dump_path}")
            except Exception as dump_error:
                logger.warning(f"Could not dump response due to error: {dump_error}")

            return {
                "file_path": str(file_path),
                "score": 1,
                "reasoning": f"Error during analysis: {str(e)}. Response dump: {dump_path}",
                "keywords_found": [],
                "confidence": "low",
                "relevant_segments": "",
                "error": str(e)
            }

    async def process_file_worker(self, file_path: Path, output_path: Path,
                                semaphore: asyncio.Semaphore, csv_lock: asyncio.Lock,
                                file_index: int = 0, total_files: int = 0) -> Dict:
        """Worker function to process a single file with concurrency control."""
        async with semaphore:
            try:
                # Print progress to console
                safe_filename = str(file_path.name).encode('ascii', errors='replace').decode('ascii')
                print(f"Processing [{file_index}/{total_files}]: {safe_filename}")

                result = await self.analyze_file(file_path, output_path)

                # Save reasoning file
                save_reasoning_file(result, output_path)

                # Save to CSV immediately (with lock to prevent concurrent writes)
                async with csv_lock:
                    save_to_csv([result], output_path)

                # Print result to console and log
                safe_file_path = str(result['file_path']).encode('ascii', errors='replace').decode('ascii')
                safe_filename = str(file_path.name).encode('ascii', errors='replace').decode('ascii')
                print(f"Completed [{file_index}/{total_files}]: {safe_filename} | Score: {result['score']}/10")
                logger.info(f"File: {safe_file_path} | Score: {result['score']}/10 | Saved to CSV")

                return result

            except Exception as e:
                # Create a safe file path for logging (remove problematic Unicode characters)
                safe_file_path = str(file_path).encode('ascii', errors='replace').decode('ascii')
                logger.error(f"Failed to analyze {safe_file_path}: {e}")
                error_result = {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"Analysis failed: {str(e)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "relevant_segments": "",
                    "error": str(e)
                }

                # Save error result to CSV immediately
                async with csv_lock:
                    save_to_csv([error_result], output_path)

                return error_result

    async def analyze_folder_async(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Async version of analyze_folder with parallel processing."""
        input_path = Path(input_folder)

        if not input_path.exists():
            raise FileNotFoundError(f"Input folder does not exist: {input_folder}")

        # Set up output folder
        if output_folder:
            output_path = Path(output_folder)
        else:
            output_path = Path("analysis_output")

        output_path.mkdir(parents=True, exist_ok=True)

        # Load already processed files
        self.processed_files = load_processed_files(output_path)

        # Find all txt files
        txt_files = find_txt_files(input_path)
        logger.info(f"Found {len(txt_files)} .txt files to analyze")

        # Filter out already processed files
        unprocessed_files = [f for f in txt_files if str(f) not in self.processed_files]

        if len(unprocessed_files) < len(txt_files):
            skipped_count = len(txt_files) - len(unprocessed_files)
            logger.info(f"Skipping {skipped_count} already processed files")

        if not unprocessed_files:
            if not txt_files:
                logger.warning("No .txt files found in the specified folder")
            else:
                logger.info("All files have already been processed")
            return []

        logger.info(f"Processing {len(unprocessed_files)} new files with {self.max_workers} workers")
        print(f"\n=== Starting analysis of {len(unprocessed_files)} files ===\n")

        # Create semaphore to limit concurrent workers and lock for CSV writing
        semaphore = asyncio.Semaphore(self.max_workers)
        csv_lock = asyncio.Lock()

        # Create tasks for all files
        tasks = [
            self.process_file_worker(file_path, output_path, semaphore, csv_lock,
                                   i + 1, len(unprocessed_files))
            for i, file_path in enumerate(unprocessed_files)
        ]

        # Process all files concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and convert them to error results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task failed for {unprocessed_files[i]}: {result}")
                processed_results.append({
                    "file_path": str(unprocessed_files[i]),
                    "score": 1,
                    "reasoning": f"Task failed: {str(result)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "relevant_segments": "",
                    "error": str(result)
                })
            else:
                processed_results.append(result)

        # Save results if we have any new ones
        if processed_results:
            # Save summary results (JSON) - CSV is already saved per file
            summary_path = output_path / "analysis_summary.json"

            # Load existing results if file exists
            all_results = []
            if summary_path.exists():
                try:
                    with open(summary_path, 'r', encoding='utf-8') as f:
                        all_results = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not load existing summary: {e}")

            # Add new results
            all_results.extend(processed_results)

            # Save updated summary
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)

        # Create error summary if there were any errors
        error_summary_path = self.error_handler.create_error_summary(output_path)
        if "No error" not in error_summary_path and "Failed to create" not in error_summary_path:
            logger.info(f"Error summary created: {error_summary_path}")
            print(f"Error summary available at: {error_summary_path}")

        logger.info(f"Analysis complete. Results saved to: {output_path}")
        print(f"\n=== Analysis complete! Processed {len(processed_results)} files ===")
        return processed_results

    def analyze_folder(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Analyze all txt files in the specified folder (synchronous wrapper for async method)."""
        return asyncio.run(self.analyze_folder_async(input_folder, output_folder))

    async def analyze_file_list_async(self, file_paths: List[Path], output_folder: str = None) -> List[Dict]:
        """Async version of analyze_file_list with parallel processing."""
        # Set up output folder
        if output_folder:
            output_path = Path(output_folder)
        else:
            output_path = Path("analysis_output")

        output_path.mkdir(parents=True, exist_ok=True)

        # Load already processed files
        self.processed_files = load_processed_files(output_path)

        # Filter out non-existent files and already processed files
        existing_files = []
        missing_files = []

        for file_path in file_paths:
            if file_path.exists():
                existing_files.append(file_path)
            else:
                missing_files.append(file_path)
                safe_file_path = str(file_path).encode('ascii', errors='replace').decode('ascii')
                logger.warning(f"File does not exist: {safe_file_path}")

        if missing_files:
            logger.warning(f"Skipping {len(missing_files)} missing files")

        logger.info(f"Found {len(existing_files)} existing files to analyze")

        # Filter out already processed files
        unprocessed_files = [f for f in existing_files if str(f) not in self.processed_files]

        if len(unprocessed_files) < len(existing_files):
            skipped_count = len(existing_files) - len(unprocessed_files)
            logger.info(f"Skipping {skipped_count} already processed files")

        if not unprocessed_files:
            if not existing_files:
                logger.warning("No valid files found to analyze")
            else:
                logger.info("All files have already been processed")
            return []

        logger.info(f"Processing {len(unprocessed_files)} new files with {self.max_workers} workers")
        print(f"\n=== Starting analysis of {len(unprocessed_files)} files ===\n")

        # Create semaphore to limit concurrent workers and lock for CSV writing
        semaphore = asyncio.Semaphore(self.max_workers)
        csv_lock = asyncio.Lock()

        # Create tasks for all files
        tasks = [
            self.process_file_worker(file_path, output_path, semaphore, csv_lock,
                                   i + 1, len(unprocessed_files))
            for i, file_path in enumerate(unprocessed_files)
        ]

        # Process all files concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and convert them to error results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task failed for {unprocessed_files[i]}: {result}")
                processed_results.append({
                    "file_path": str(unprocessed_files[i]),
                    "score": 1,
                    "reasoning": f"Task failed: {str(result)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "relevant_segments": "",
                    "error": str(result)
                })
            else:
                processed_results.append(result)

        # Save results if we have any new ones
        if processed_results:
            # Save summary results (JSON) - CSV is already saved per file
            summary_path = output_path / "analysis_summary.json"

            # Load existing results if file exists
            all_results = []
            if summary_path.exists():
                try:
                    with open(summary_path, 'r', encoding='utf-8') as f:
                        all_results = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not load existing summary: {e}")

            # Add new results
            all_results.extend(processed_results)

            # Save updated summary
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)

        # Create error summary if there were any errors
        error_summary_path = self.error_handler.create_error_summary(output_path)
        if "No error" not in error_summary_path and "Failed to create" not in error_summary_path:
            logger.info(f"Error summary created: {error_summary_path}")
            print(f"Error summary available at: {error_summary_path}")

        logger.info(f"Analysis complete. Results saved to: {output_path}")
        print(f"\n=== Analysis complete! Processed {len(processed_results)} files ===")
        return processed_results

    def analyze_file_list(self, file_paths: List[Path], output_folder: str = None) -> List[Dict]:
        """Analyze specific files from a list (synchronous wrapper for async method)."""
        return asyncio.run(self.analyze_file_list_async(file_paths, output_folder))
