#!/usr/bin/env python3
"""
Test script to verify error handling and response dumping functionality.
"""

import json
import tempfile
from pathlib import Path
from analyzer import LayoutBricksAnaly<PERSON>

def test_error_dump_functionality():
    """Test the error dumping functionality with a mock scenario."""

    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create test analyzer
        analyzer = LayoutBricksAnalyzer()

        # Test the dump function directly
        test_response = '''{"score": 8, "reasoning": "This text contains multiple references to layout bricks and UI components", "keywords_found": ["layout", "bricks", "component"], "confidence": "high", "relevant_segments": "00:01:30,000 -> 00:02:15,000"}'''

        test_file_path = temp_path / "test_file.txt"
        test_file_path.write_text("Test content")

        # Test successful dump
        dump_path = analyzer.error_handler.dump_response_to_file(
            test_response,
            test_file_path,
            "Test error message",
            temp_path
        )

        print(f"✓ Response dumped to: {dump_path}")

        # Verify the dump file was created
        if Path(dump_path).exists():
            print("✓ Dump file exists")
            with open(dump_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "RAW RESPONSE CONTENT:" in content and test_response in content:
                    print("✓ Dump file contains expected content")
                else:
                    print("✗ Dump file missing expected content")
        else:
            print("✗ Dump file was not created")

        # Test with disabled error dumping
        analyzer.config.enable_error_dumping = False
        disabled_dump_path = analyzer.error_handler.dump_response_to_file(
            test_response,
            test_file_path,
            "Test with dumping disabled",
            temp_path
        )
        print(f"✓ Disabled dump result: {disabled_dump_path}")

        # Re-enable for summary test
        analyzer.config.enable_error_dumping = True

        # Test error summary creation
        summary_path = analyzer.error_handler.create_error_summary(temp_path)
        print(f"✓ Error summary created: {summary_path}")

        if Path(summary_path).exists():
            print("✓ Error summary file exists")
            with open(summary_path, 'r', encoding='utf-8') as f:
                summary_content = f.read()
                if "ERROR SUMMARY" in summary_content:
                    print("✓ Error summary contains expected header")
                else:
                    print("✗ Error summary missing expected header")
        else:
            print("✗ Error summary file was not created")

def test_large_response_truncation():
    """Test truncation of large responses."""

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create test analyzer with small max size
        analyzer = LayoutBricksAnalyzer()
        analyzer.config.error_dump_max_size = 100  # Very small for testing

        # Create a large response
        large_response = "x" * 500  # 500 characters

        test_file_path = temp_path / "large_test.txt"
        test_file_path.write_text("Test content")

        dump_path = analyzer.error_handler.dump_response_to_file(
            large_response,
            test_file_path,
            "Test large response truncation",
            temp_path
        )

        print(f"✓ Large response dumped to: {dump_path}")

        # Check if truncation occurred
        if Path(dump_path).exists():
            with open(dump_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "Content Truncated: Yes" in content and "CONTENT TRUNCATED" in content:
                    print("✓ Large response was properly truncated")
                else:
                    print("✗ Large response truncation not working")
        else:
            print("✗ Large response dump file not created")

def test_json_parsing_error_simulation():
    """Simulate a JSON parsing error to test error handling."""

    # Test malformed JSON responses
    malformed_responses = [
        '{"score": 8, "reasoning": "This text contains "layout bricks" references", "keywords_found": ["layout"]}',  # Unescaped quotes
        '{"score": 8, "reasoning": "This text contains',  # Unterminated string
        '{"score": 8, "reasoning": "Test", "keywords_found": ["layout"], "confidence": "high"',  # Missing closing brace
        'Not JSON at all - just plain text response',  # Not JSON
        '',  # Empty response
    ]

    print("\n=== Testing JSON parsing error scenarios ===")

    for i, response in enumerate(malformed_responses, 1):
        print(f"\nTest {i}: Testing malformed response")
        try:
            json.loads(response)
            print(f"  ✗ Expected JSON parsing to fail, but it succeeded")
        except json.JSONDecodeError as e:
            print(f"  ✓ JSON parsing failed as expected: {str(e)}")
            print(f"  Response length: {len(response)} characters")
            if len(response) > 50:
                print(f"  Response preview: {response[:50]}...")
            else:
                print(f"  Response: {response}")

if __name__ == "__main__":
    print("=== Testing Error Handling Functionality ===\n")

    try:
        test_error_dump_functionality()
        print("\n" + "="*50)
        test_large_response_truncation()
        print("\n" + "="*50)
        test_json_parsing_error_simulation()
        print("\n✓ All tests completed successfully!")

    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
