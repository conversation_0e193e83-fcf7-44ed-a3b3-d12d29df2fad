#!/usr/bin/env python3
"""
Audio Segment Extractor

This script processes analysis results CSV file and extracts audio segments
from corresponding MP3/M4A files based on timestamps and scores.
"""

import csv
import os
import re
import subprocess
import sys
from pathlib import Path
from typing import List, Tuple, Optional

# Constants
DEFAULT_CSV_PATH = r"D:\Temp\d\a_txt_or_analysis\analysis_results.csv"
DEFAULT_AUDIO_SEARCH_PATH = r"D:\Temp\d\a"
DEFAULT_OUTPUT_PATH = r"D:\Temp\d\a_oral_segments"
MIN_SCORE_THRESHOLD = 4
SEGMENT_MERGE_THRESHOLD_SECONDS = 35  # 2 minutes

def parse_timestamp(timestamp_str: str) -> float:
    """Convert timestamp string [HH:MM:SS,ms] to seconds."""
    # Remove brackets and split by comma
    time_part = timestamp_str.strip('[]').split(',')[0]
    parts = time_part.split(':')

    if len(parts) == 3:
        hours, minutes, seconds = map(int, parts)
        return hours * 3600 + minutes * 60 + seconds
    elif len(parts) == 2:
        minutes, seconds = map(int, parts)
        return minutes * 60 + seconds
    else:
        return float(parts[0])

def parse_relevant_segments(segments_str: str) -> List[Tuple[float, float]]:
    """Parse relevant_segments string into list of (start, end) tuples in seconds."""
    if not segments_str or segments_str.strip() == "":
        return []

    segments = []
    # Split by semicolon to get individual segments
    segment_parts = segments_str.split(';')

    for segment in segment_parts:
        segment = segment.strip()
        if not segment:
            continue

        # Match pattern [HH:MM:SS,ms -> HH:MM:SS,ms]
        match = re.match(r'\[([^\]]+)\s*->\s*([^\]]+)\]', segment)
        if match:
            start_str, end_str = match.groups()
            start_time = parse_timestamp(f'[{start_str}]')
            end_time = parse_timestamp(f'[{end_str}]')
            segments.append((start_time, end_time))

    return segments

def merge_close_segments(segments: List[Tuple[float, float]], threshold: int = SEGMENT_MERGE_THRESHOLD_SECONDS) -> Tuple[List[Tuple[float, float]], float]:
    """Merge segments that are less than threshold seconds apart.

    Returns:
        Tuple of (merged_segments, time_added_by_merging)
    """
    if not segments:
        return [], 0.0

    # Sort segments by start time
    segments = sorted(segments)
    merged = [segments[0]]
    time_added = 0.0

    for current_start, current_end in segments[1:]:
        last_start, last_end = merged[-1]

        # If current segment starts within threshold of last segment's end, merge them
        if current_start - last_end <= threshold:
            # Calculate time added by filling the gap
            gap_time = current_start - last_end
            time_added += gap_time
            merged[-1] = (last_start, max(last_end, current_end))
        else:
            merged.append((current_start, current_end))

    return merged, time_added

def find_audio_file(filename_without_ext: str, search_path: str) -> Optional[str]:
    """Find corresponding MP3 or M4A file recursively."""
    # Try both mp3 and m4a extensions
    for ext in ['.mp3', '.m4a']:
        target_filename = f"{filename_without_ext}{ext}"

        # Use os.walk to avoid glob pattern matching issues with special characters
        for root, dirs, files in os.walk(search_path):
            for file in files:
                if file == target_filename:
                    return os.path.join(root, file)

    return None

def get_audio_codec(input_file: str) -> str:
    """Get the actual audio codec of the input file using ffprobe."""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-select_streams', 'a:0',
            '-show_entries', 'stream=codec_name', '-of', 'csv=p=0',
            input_file
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.strip().lower()
    except Exception:
        pass
    return 'unknown'

def extract_audio_segment(input_file: str, output_file: str, segments: List[Tuple[float, float]]) -> bool:
    """Extract audio segments using ffmpeg."""
    try:
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Determine if we can use codec copy or need to re-encode
        output_ext = Path(output_file).suffix.lower()

        # Get actual codec of input file (not just extension)
        input_codec = get_audio_codec(input_file)

        # Use codec copy only if input is already MP3 and output is MP3
        use_copy = (input_codec == 'mp3' and output_ext == '.mp3')

        if len(segments) == 1:
            # Single segment
            start, end = segments[0]
            duration = end - start

            if use_copy:
                cmd = [
                    'ffmpeg', '-i', input_file,
                    '-ss', str(start),
                    '-t', str(duration),
                    '-c', 'copy',
                    '-y',  # Overwrite output file
                    output_file
                ]
            else:
                cmd = [
                    'ffmpeg', '-i', input_file,
                    '-ss', str(start),
                    '-t', str(duration),
                    '-acodec', 'libmp3lame',
                    '-ab', '128k',
                    '-y',  # Overwrite output file
                    output_file
                ]
        else:
            # Multiple segments - create a filter complex
            # Note: Cannot use codec copy with filter complex, must re-encode
            filter_parts = []
            input_parts = []

            for i, (start, end) in enumerate(segments):
                duration = end - start
                filter_parts.append(f"[0:a]atrim=start={start}:duration={duration},asetpts=PTS-STARTPTS[a{i}]")
                input_parts.append(f"[a{i}]")

            # Concatenate all segments
            filter_complex = ';'.join(filter_parts) + f";{' '.join(input_parts)}concat=n={len(segments)}:v=0:a=1[out]"

            # Always re-encode when using filter complex (cannot use codec copy)
            cmd = [
                'ffmpeg', '-i', input_file,
                '-filter_complex', filter_complex,
                '-map', '[out]',
                '-acodec', 'libmp3lame',
                '-ab', '128k',
                '-y',  # Overwrite output file
                output_file
            ]

        # Run ffmpeg command
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✓ Successfully extracted: {output_file}")
            return True
        else:
            print(f"✗ Error extracting {output_file}")
            print(f"  Input codec detected: {input_codec}")
            print(f"  Use copy mode: {use_copy}")
            print(f"  FFmpeg error: {result.stderr}")
            return False

    except Exception as e:
        print(f"✗ Exception extracting {output_file}: {str(e)}")
        return False

def process_csv_file(csv_path: str, audio_search_path: str, output_path: str):
    """Process the CSV file and extract audio segments."""
    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found: {csv_path}")
        return

    # Create output directory
    os.makedirs(output_path, exist_ok=True)

    processed_count = 0
    success_count = 0
    total_time_added = 0.0
    total_merges = 0

    with open(csv_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)

        for row in reader:
            try:
                # Get values from row
                file_path = row['file_path']
                score = int(row['score'])
                relevant_segments = row.get('relevant_segments', '')

                # Skip files with score <= threshold
                if score <= MIN_SCORE_THRESHOLD:
                    continue

                # Extract filename without extension
                filename = os.path.basename(file_path)
                filename_without_ext = os.path.splitext(filename)[0]

                print(f"\nProcessing: {filename} (Score: {score})")

                # Find corresponding audio file
                audio_file = find_audio_file(filename_without_ext, audio_search_path)
                if not audio_file:
                    print(f"  ✗ Audio file not found for: {filename}")
                    continue

                print(f"  ✓ Found audio file: {audio_file}")

                # Parse segments
                segments = parse_relevant_segments(relevant_segments)
                if not segments:
                    print(f"  ✗ No relevant segments found for: {filename}")
                    continue

                print(f"  ✓ Found {len(segments)} segments")

                # Calculate original total duration
                original_duration = sum(end - start for start, end in segments)

                # Merge close segments
                merged_segments, time_added = merge_close_segments(segments)
                if len(merged_segments) < len(segments):
                    merged_duration = sum(end - start for start, end in merged_segments)
                    print(f"  ✓ Merged to {len(merged_segments)} segments")
                    print(f"  ✓ Time added by merging: {time_added:.1f} seconds ({time_added/60:.1f} minutes)")
                    print(f"  ✓ Original duration: {original_duration:.1f}s, Merged duration: {merged_duration:.1f}s")
                    print(f"  ✓ Duration increase: {((merged_duration - original_duration) / original_duration * 100):.1f}%")
                    total_time_added += time_added
                    total_merges += 1

                # Create output filename with score prefix
                output_filename = f"{score}_{filename_without_ext}.mp3"
                output_file = os.path.join(output_path, output_filename)

                # Extract segments
                if extract_audio_segment(audio_file, output_file, merged_segments):
                    success_count += 1

                processed_count += 1

            except Exception as e:
                print(f"  ✗ Error processing row: {str(e)}")
                continue

    print(f"\n=== Summary ===")
    print(f"Files processed: {processed_count}")
    print(f"Successfully extracted: {success_count}")
    print(f"Files with merged segments: {total_merges}")
    print(f"Total time added by merging: {total_time_added:.1f} seconds ({total_time_added/60:.1f} minutes)")
    if total_merges > 0:
        print(f"Average time added per merge: {total_time_added/total_merges:.1f} seconds")
    print(f"Merge threshold: {SEGMENT_MERGE_THRESHOLD_SECONDS} seconds ({SEGMENT_MERGE_THRESHOLD_SECONDS/60:.1f} minutes)")
    print(f"Output directory: {output_path}")

def main():
    """Main function."""
    print("Audio Segment Extractor")
    print("=" * 50)

    # Check if ffmpeg is available
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: ffmpeg not found. Please install ffmpeg and add it to PATH.")
        sys.exit(1)

    # Process the CSV file
    process_csv_file(DEFAULT_CSV_PATH, DEFAULT_AUDIO_SEARCH_PATH, DEFAULT_OUTPUT_PATH)

if __name__ == "__main__":
    main()
