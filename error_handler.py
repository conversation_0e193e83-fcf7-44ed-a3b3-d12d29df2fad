"""
Error handling utilities for the Layout Bricks Instructions Analyzer.

This module handles error dumping, logging, and debugging functionality
separate from the main analysis logic.
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional

logger = logging.getLogger(__name__)


class ErrorHandler:
    """Handles error dumping and debugging functionality."""

    def __init__(self, config):
        """Initialize error handler with configuration."""
        self.config = config

    def dump_response_to_file(self, response_content: str, file_path: Path, 
                             error_msg: str, output_folder: Path = None) -> str:
        """Dump model response content to a file for debugging purposes."""
        try:
            # Check if error dumping is enabled
            if not self.config.enable_error_dumping:
                return "Error dumping disabled in configuration"
            
            # Create error logs folder
            if output_folder:
                error_logs_path = output_folder / "error_logs"
            else:
                error_logs_path = Path("analysis_output") / "error_logs"
            
            error_logs_path.mkdir(parents=True, exist_ok=True)
            
            # Clean up old error dumps if we have too many
            self._cleanup_old_dumps(error_logs_path)
            
            # Create timestamp for unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
            
            # Create safe filename from original file path
            safe_filename = str(file_path.name).replace('.txt', '').encode('ascii', errors='replace').decode('ascii')
            safe_filename = ''.join(c for c in safe_filename if c.isalnum() or c in '-_')[:50]  # Limit length
            
            # Create error dump filename
            dump_filename = f"response_dump_{safe_filename}_{timestamp}.txt"
            dump_path = error_logs_path / dump_filename
            
            # Truncate response content if it's too large
            truncated_content, was_truncated = self._truncate_content(response_content)

            # Write the response content and error details to file
            with open(dump_path, 'w', encoding='utf-8', errors='replace') as f:
                f.write(f"=== MODEL RESPONSE DUMP ===\n")
                f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                f.write(f"Original File: {file_path}\n")
                f.write(f"Error: {error_msg}\n")
                f.write(f"Response Length: {len(response_content)} characters\n")
                if was_truncated:
                    f.write(f"Content Truncated: Yes (showing first {self.config.error_dump_max_size} characters)\n")
                f.write(f"{'='*50}\n\n")
                f.write("RAW RESPONSE CONTENT:\n")
                f.write(f"{'='*50}\n")
                f.write(truncated_content)
                if was_truncated:
                    f.write(f"\n\n[CONTENT TRUNCATED - Original length: {len(response_content)} characters]")
                f.write(f"\n{'='*50}\n")
                f.write("END OF RESPONSE\n")
            
            logger.info(f"Response content dumped to: {dump_path}")
            return str(dump_path)
            
        except Exception as dump_error:
            logger.error(f"Failed to dump response content: {dump_error}")
            return f"Failed to dump response: {dump_error}"

    def _cleanup_old_dumps(self, error_logs_path: Path):
        """Clean up old error dumps if we have too many."""
        existing_dumps = list(error_logs_path.glob("response_dump_*.txt"))
        if len(existing_dumps) >= self.config.max_error_dumps:
            # Sort by modification time and remove oldest
            existing_dumps.sort(key=lambda x: x.stat().st_mtime)
            for old_dump in existing_dumps[:-self.config.max_error_dumps + 1]:
                try:
                    old_dump.unlink()
                    logger.debug(f"Removed old error dump: {old_dump}")
                except Exception as cleanup_error:
                    logger.warning(f"Could not remove old error dump {old_dump}: {cleanup_error}")

    def _truncate_content(self, content: str) -> tuple[str, bool]:
        """Truncate content if it's too large."""
        if len(content) > self.config.error_dump_max_size:
            return content[:self.config.error_dump_max_size], True
        return content, False

    def create_error_summary(self, output_folder: Path = None) -> str:
        """Create a summary of all error dumps for easier debugging."""
        try:
            if output_folder:
                error_logs_path = output_folder / "error_logs"
            else:
                error_logs_path = Path("analysis_output") / "error_logs"
            
            if not error_logs_path.exists():
                return "No error logs found"
            
            # Find all dump files
            dump_files = list(error_logs_path.glob("response_dump_*.txt"))
            
            if not dump_files:
                return "No response dump files found"
            
            # Create summary file
            summary_path = error_logs_path / "error_summary.txt"
            
            with open(summary_path, 'w', encoding='utf-8', errors='replace') as summary_file:
                summary_file.write(f"=== ERROR SUMMARY ===\n")
                summary_file.write(f"Generated: {datetime.now().isoformat()}\n")
                summary_file.write(f"Total error dumps: {len(dump_files)}\n")
                summary_file.write(f"{'='*50}\n\n")
                
                for dump_file in sorted(dump_files):
                    self._write_dump_summary(summary_file, dump_file)
            
            logger.info(f"Error summary created: {summary_path}")
            return str(summary_path)
            
        except Exception as summary_error:
            logger.error(f"Failed to create error summary: {summary_error}")
            return f"Failed to create error summary: {summary_error}"

    def _write_dump_summary(self, summary_file, dump_file: Path):
        """Write summary information for a single dump file."""
        try:
            with open(dump_file, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
                
            # Extract key information from dump file
            lines = content.split('\n')
            original_file = "Unknown"
            error_msg = "Unknown"
            response_length = "Unknown"
            
            for line in lines:
                if line.startswith("Original File:"):
                    original_file = line.replace("Original File:", "").strip()
                elif line.startswith("Error:"):
                    error_msg = line.replace("Error:", "").strip()
                elif line.startswith("Response Length:"):
                    response_length = line.replace("Response Length:", "").strip()
            
            summary_file.write(f"File: {dump_file.name}\n")
            summary_file.write(f"  Original: {original_file}\n")
            summary_file.write(f"  Error: {error_msg}\n")
            summary_file.write(f"  Response Length: {response_length}\n")
            summary_file.write(f"  Dump Path: {dump_file}\n")
            summary_file.write(f"{'-'*30}\n")
            
        except Exception as file_error:
            summary_file.write(f"File: {dump_file.name}\n")
            summary_file.write(f"  Error reading dump file: {file_error}\n")
            summary_file.write(f"{'-'*30}\n")

    def handle_json_parse_error(self, response_text: str, file_path: Path, 
                               error: json.JSONDecodeError, output_folder: Path = None) -> dict:
        """Handle JSON parsing errors with response dumping."""
        safe_file_path = str(file_path).encode('ascii', errors='replace').decode('ascii')
        error_msg = f"Failed to parse structured JSON response: {str(error)}"
        logger.warning(f"{error_msg} for {safe_file_path}")
        logger.debug(f"Raw response (first 500 chars): {response_text[:500]}")

        # Dump the full response content to a file for debugging
        dump_path = self.dump_response_to_file(response_text, file_path, error_msg, output_folder)
        logger.error(f"Full response content dumped to: {dump_path}")

        # Try to extract basic information from malformed JSON
        score = self._extract_score_from_response(response_text)
        reasoning = f"Failed to parse structured response: {str(error)}. Response dumped to: {dump_path}"

        return {
            "score": score,
            "reasoning": reasoning,
            "keywords_found": [],
            "confidence": "low",
            "relevant_segments": ""
        }

    def _extract_score_from_response(self, response_text: str) -> int:
        """Try to extract score from malformed JSON response."""
        try:
            import re
            score_match = re.search(r'"score":\s*(\d+)', response_text)
            if score_match:
                return min(10, max(1, int(score_match.group(1))))
        except:
            pass
        return 1
